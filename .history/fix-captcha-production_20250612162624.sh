#!/bin/bash

# Fix CAPTCHA Configuration for FaceTrace Production
# This script updates the reCAPTCHA configuration to use the correct keys for facetracepro project

set -e

PROJECT_ID="realestate-2b2bf"
SERVICE_NAME="facetrace-backend-api"
REGION="us-central1"

echo "🔧 Fixing CAPTCHA configuration for FaceTrace Production..."
echo "📋 Project: $PROJECT_ID"
echo "🚀 Service: $SERVICE_NAME"
echo "🌍 Region: $REGION"
echo ""

# Check if service exists
echo "🔍 Checking if service exists..."
if ! sudo gcloud run services describe $SERVICE_NAME --region $REGION --project $PROJECT_ID &>/dev/null; then
    echo "❌ Error: Service '$SERVICE_NAME' not found in region '$REGION'"
    echo "📝 Please check existing services with:"
    echo "   sudo gcloud run services list --project $PROJECT_ID"
    exit 1
fi

echo "✅ Service found. Proceeding with CAPTCHA configuration fix..."
echo ""

# Update reCAPTCHA configuration with correct keys for facetracepro project
echo "🔐 Updating reCAPTCHA configuration..."
echo "   Site Key: 6LfTgVUrAAAAAN55-pHaO68HtiFzPs92FbXQ9d92"
echo "   Secret Key: 6LfTgVUrAAAAAIV7Y8puVbNGGHsVoHLJKBvftw7H"
echo "   Project: facetracepro"

sudo gcloud run services update $SERVICE_NAME \
  --region $REGION \
  --project $PROJECT_ID \
  --set-env-vars NEXT_PUBLIC_RECAPTCHA_SITE_KEY="6LfTgVUrAAAAAN55-pHaO68HtiFzPs92FbXQ9d92",RECAPTCHA_SECRET_KEY="6LfTgVUrAAAAAIV7Y8puVbNGGHsVoHLJKBvftw7H"

echo "✅ reCAPTCHA keys updated!"
echo ""

# Update Google Cloud project configuration
echo "🌐 Updating Google Cloud project configuration..."
sudo gcloud run services update $SERVICE_NAME \
  --region $REGION \
  --project $PROJECT_ID \
  --set-env-vars GOOGLE_CLOUD_PROJECT_ID="facetracepro",NEXT_PUBLIC_GOOGLE_CLOUD_PROJECT_ID="facetracepro"

echo "✅ Google Cloud project configuration updated!"
echo ""

# Ensure reCAPTCHA Enterprise is properly configured
echo "🛡️ Updating reCAPTCHA Enterprise settings..."
sudo gcloud run services update $SERVICE_NAME \
  --region $REGION \
  --project $PROJECT_ID \
  --set-env-vars FORCE_STANDARD_RECAPTCHA="false",NEXT_PUBLIC_FORCE_STANDARD_RECAPTCHA="false",BYPASS_CAPTCHA_IN_DEV="false",TEST_CAPTCHA_VERIFICATION="false"

echo "✅ reCAPTCHA Enterprise settings updated!"
echo ""

# Wait for deployment to complete
echo "⏳ Waiting for deployment to complete..."
sleep 10

# Verify the configuration
echo "🔍 Verifying the updated configuration..."
echo ""
echo "📋 Current environment variables:"
sudo gcloud run services describe $SERVICE_NAME --region $REGION --project $PROJECT_ID --format="value(spec.template.spec.template.spec.containers[0].env[].name,spec.template.spec.template.spec.containers[0].env[].value)" | grep -E "(RECAPTCHA|GOOGLE_CLOUD_PROJECT)" || echo "No matching environment variables found"

echo ""
echo "✅ CAPTCHA configuration fix completed!"
echo ""
echo "🧪 Next steps:"
echo "1. Test CAPTCHA functionality on https://facetrace.pro"
echo "2. Check the browser console for any CAPTCHA errors"
echo "3. Monitor the application logs for CAPTCHA verification results"
echo ""
echo "🔍 If issues persist, check:"
echo "   - Domain configuration in Google reCAPTCHA console"
echo "   - Site key matches the secret key project"
echo "   - facetrace.pro is added to allowed domains"
echo ""
echo "📊 Monitor logs with:"
echo "   sudo gcloud logs tail --project=$PROJECT_ID --filter='resource.type=\"cloud_run_revision\" AND resource.labels.service_name=\"$SERVICE_NAME\"'"
