#!/bin/bash

# FaceTrace Backend API Environment Variables Setup
# Run this script after the initial deployment to configure environment variables

set -e

PROJECT_ID="realestate-2b2bf"
SERVICE_NAME="facetrace-backend-api"
REGION="us-central1"

echo "🔧 Setting up environment variables for FaceTrace Backend API..."

# Check if service exists
echo "🔍 Checking if service exists..."
if ! gcloud run services describe $SERVICE_NAME --region $REGION --project $PROJECT_ID &>/dev/null; then
    echo "❌ Error: Service '$SERVICE_NAME' not found in region '$REGION'"
    echo "📝 Please run the deployment script first: ./deploy-backend.sh"
    echo "🔍 Or check existing services with:"
    echo "   gcloud run services list --project $PROJECT_ID"
    exit 1
fi

echo "✅ Service found. Proceeding with environment variable setup..."

# Core application settings
echo "📋 Setting core application variables..."
gcloud run services update $SERVICE_NAME \
  --region $REGION \
  --project $PROJECT_ID \
  --set-env-vars NODE_ENV=production,NEXT_PUBLIC_DISABLE_AUTH=true,NEXT_PUBLIC_DISABLE_PAYMENT=true

# Database configuration
echo "🗄️ Setting database configuration..."
gcloud run services update $SERVICE_NAME \
  --region $REGION \
  --project $PROJECT_ID \
  --set-env-vars DATABASE_URL="postgresql://facetracepro_owner:<EMAIL>/facetracepro?sslmode=require"

# reCAPTCHA configuration - Updated for facetracepro project
echo "🔐 Setting reCAPTCHA configuration..."
gcloud run services update $SERVICE_NAME \
  --region $REGION \
  --project $PROJECT_ID \
  --set-env-vars NEXT_PUBLIC_RECAPTCHA_SITE_KEY="6LfTgVUrAAAAAN55-pHaO68HtiFzPs92FbXQ9d92",RECAPTCHA_SECRET_KEY="6LfTgVUrAAAAAIV7Y8puVbNGGHsVoHLJKBvftw7H"

# External API configurations
echo "🌐 Setting external API configurations..."
gcloud run services update $SERVICE_NAME \
  --region $REGION \
  --project $PROJECT_ID \
  --set-env-vars FACECHECK_API_TOKEN="PVP7q4Otamgc3SW+cGUIx5ce4nRe5P4k7ZbN4m7Z9aYlmoyulnQ+Ji5eDySlcYT0F8JE0E8DEVQ=",FACECHECK_API_KEY="PVP7q4Otamgc3SW+cGUIx5ce4nRe5P4k7ZbN4m7Z9aYlmoyulnQ+Ji5eDySlcYT0F8JE0E8DEVQ="

# Additional critical environment variables - Updated for realestate-2b2bf project
echo "🔧 Setting additional environment variables..."
gcloud run services update $SERVICE_NAME \
  --region $REGION \
  --project $PROJECT_ID \
  --set-env-vars GOOGLE_CLOUD_PROJECT_ID="realestate-2b2bf",NEXT_PUBLIC_GOOGLE_CLOUD_PROJECT_ID="realestate-2b2bf",GOOGLE_CLOUD_PROJECT_NUMBER="202194517542",GOOGLE_CLOUD_API_KEY="AIzaSyCzQcHaY9eOpYIlHF2tOLJ6y-3i81Lt7-c"

# Google Gemini API
echo "🤖 Setting Google Gemini API configuration..."
gcloud run services update $SERVICE_NAME \
  --region $REGION \
  --project $PROJECT_ID \
  --set-env-vars GOOGLE_GEMINI_API_KEY="AIzaSyD1xky8CGntRq8AatJ2sY-S54_9n9UWUcg",NEXT_PUBLIC_GOOGLE_GEMINI_API_KEY="AIzaSyD1xky8CGntRq8AatJ2sY-S54_9n9UWUcg"

# Email configuration
echo "📧 Setting email configuration..."
gcloud run services update $SERVICE_NAME \
  --region $REGION \
  --project $PROJECT_ID \
  --set-env-vars RESEND_API_KEY="re_25uEt5vF_7xNhQWaH7Sh7HkYizKuWYi8w",RESEND_SENDER_EMAIL="<EMAIL>"

# Logging and monitoring
echo "📊 Setting logging configuration..."
gcloud run services update $SERVICE_NAME \
  --region $REGION \
  --project $PROJECT_ID \
  --set-env-vars LOG_LEVEL=info

# Sentry configuration
echo "🔍 Setting Sentry monitoring configuration..."
gcloud run services update $SERVICE_NAME \
  --region $REGION \
  --project $PROJECT_ID \
  --set-env-vars NEXT_PUBLIC_SENTRY_DSN="https://<EMAIL>/4509128771239936",SENTRY_AUTH_TOKEN="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",SENTRY_ORG="facetracepro",SENTRY_PROJECT="javascript-nextjs",SENTRY_LOG_LEVEL="info"

# Application URLs and CRON
echo "🌐 Setting application URLs and CRON configuration..."
gcloud run services update $SERVICE_NAME \
  --region $REGION \
  --project $PROJECT_ID \
  --set-env-vars NEXT_PUBLIC_APP_URL="https://facetrace.pro",CRON_SECRET="arzUvi2aVni2lqpxd8CMBACqe6pknPQlmgO9uQ0G0gw="

# reCAPTCHA additional settings
echo "🛡️ Setting additional reCAPTCHA settings..."
gcloud run services update $SERVICE_NAME \
  --region $REGION \
  --project $PROJECT_ID \
  --set-env-vars FORCE_STANDARD_RECAPTCHA="false",NEXT_PUBLIC_FORCE_STANDARD_RECAPTCHA="false",BYPASS_CAPTCHA_IN_DEV="false",TEST_CAPTCHA_VERIFICATION="false"

echo "✅ Environment variables setup completed!"
echo ""
echo "🔍 You can verify the configuration with:"
echo "   gcloud run services describe $SERVICE_NAME --region $REGION --project $PROJECT_ID"
